"use client";

import Link from "next/link";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { Logo } from "@/components/atoms";
import { LocaleSwitcher } from "@/components/molecules/LocaleSwitcher";
import { Button } from "@/components/atoms/Button";
import { SignInModal } from "@/components/organisms/Auth";
import { cn } from "@/lib/utils";
import { Menu, X } from "lucide-react";

interface LandingNavbarProps {
  className?: string;
}

const navigationItems = [
  { key: "home", href: "/" },
  { key: "whatWeDo", href: "/what-we-do" },
  { key: "company", href: "/company" },
];

export function LandingNavbar({ className }: Readonly<LandingNavbarProps>) {
  const t = useTranslations("common");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSignInModalOpen, setIsSignInModalOpen] = useState(false);

  return (
    <>
      <nav
        className={cn(
          "bg-white/95 backdrop-blur-sm shadow-sm border-b border-gray-200 sticky top-0 z-50",
          className
        )}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left side - Logo */}
            <div className="flex items-center">
              <Logo size="sm" href="/" />
              <Link href="/" className="ml-3 group">
                <span className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                  CheckU
                </span>
              </Link>
            </div>

            {/* Center - Navigation Menu */}
            <div className="hidden md:flex items-center space-x-1">
              {navigationItems.map((item) => (
                <Link
                  key={item.key}
                  href={item.href}
                  className="text-gray-600 hover:text-blue-600 px-4 py-2 text-sm font-medium transition-all duration-200 hover:bg-blue-50 rounded-lg relative group"
                >
                  {t(item.key) || item.key}
                  <span className="absolute bottom-0 left-1/2 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-3/4 transform -translate-x-1/2"></span>
                </Link>
              ))}
            </div>

            {/* Right side - Auth Links and Language Switcher */}
            <div className="flex items-center space-x-4">
              {/* Sign In Button */}
              <div className="hidden sm:flex items-center">
                <Button
                  size="sm"
                  onClick={() => setIsSignInModalOpen(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  {t("signIn") || "Sign In"}
                </Button>
              </div>

              {/* Language Switcher */}
              <LocaleSwitcher />

              {/* Mobile menu button */}
              <div className="md:hidden">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                >
                  {isMobileMenuOpen ? (
                    <X className="h-4 w-4" />
                  ) : (
                    <Menu className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Mobile menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden border-t border-gray-200 pt-4 pb-4 bg-white">
              <div className="space-y-1">
                {navigationItems.map((item) => (
                  <Link
                    key={item.key}
                    href={item.href}
                    className="block text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-4 py-3 text-sm font-medium transition-all duration-200 rounded-lg mx-2"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {t(item.key) || item.key}
                  </Link>
                ))}

                {/* Mobile sign in button */}
                <div className="pt-4 px-2">
                  <Button
                    size="sm"
                    onClick={() => {
                      setIsSignInModalOpen(true);
                      setIsMobileMenuOpen(false);
                    }}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white transition-all duration-200 shadow-sm hover:shadow-md"
                  >
                    {t("signIn") || "Sign In"}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Sign In Modal - Outside nav to avoid z-index conflicts */}
      <SignInModal
        isOpen={isSignInModalOpen}
        onClose={() => setIsSignInModalOpen(false)}
      />
    </>
  );
}

export default LandingNavbar;
